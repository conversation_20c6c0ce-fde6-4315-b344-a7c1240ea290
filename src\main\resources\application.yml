reader:
  app:
    storagePath: storage
    showUI: false
    debug: false
    packaged: false
    secure: false
    inviteCode: ""
    secureKey: ""
    proxy: false
    proxyType: "HTTP"
    proxyHost: ""
    proxyPort: ""
    proxyUsername: ""
    proxyPassword: ""
    cacheChapterContent: true
    userLimit: 50
    userBookLimit: 200
    debugLog: false
    autoClearInactiveUser: 0

  server:
    port: 8080
    webUrl: http://localhost:${reader.server.port}

logging:
  path: "./logs"