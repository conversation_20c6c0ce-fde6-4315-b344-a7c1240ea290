name: Build Docker Image
on:
  push:
    branches:
      - master
jobs:
  build:
    if: github.repository == 'hectorqin/reader'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master
    - name: Publish to Registry
      uses: elgohr/Publish-Docker-Github-Action@master
      with:
        name: hectorqin/reader-basic
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        snapshot: true
        tags: "test"
