<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <springProperty scope="context" name="logPath" source="logging.path" defaultValue="./logs" />

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </encoder>
    </appender>


    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/reader-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>[%thread] %d{HH:mm:ss.SSS} %-5level %logger{0} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="file" />
    </appender>

    <logger name="io.netty" level="warn"/>
    <!--    <logger name="io.vertx" level="TRACE"/>-->
    <logger name="com.htmake.reader" level="debug"/>

    <springProfile name="default">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>


    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="ASYNC_ROLLING_FILE"/>
        </root>
    </springProfile>

</configuration>