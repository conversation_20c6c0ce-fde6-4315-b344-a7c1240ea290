@charset "UTF-8";
@font-face {
  font-family: "iconfont";
  src: url("./iconfont.woff") format("woff");
}

@font-face {
  font-family: "reader-iconfont"; /* Project id 2841133 */
  src: url('./reader-iconfont.woff2?t=1657702223137') format('woff2'),
       url('./reader-iconfont.woff?t=1657702223137') format('woff'),
       url('./reader-iconfont.ttf?t=1657702223137') format('truetype');
}

.reader-iconfont {
  font-family: "reader-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.reader-icon-shezhi:before {
  content: "\e654";
}

.reader-icon-volume-off:before {
  content: "\e631";
}

.reader-icon-volume:before {
  content: "\e632";
}

.reader-icon-15s:before {
  content: "\e61f";
}

.reader-icon-jian15s:before {
  content: "\e620";
}

.reader-icon-player-pause:before {
  content: "\ea2b";
}

.reader-icon-player-forward-step:before {
  content: "\ea2c";
}

.reader-icon-player-backward-step:before {
  content: "\ea2d";
}

.reader-icon-player-play:before {
  content: "\ea2e";
}

