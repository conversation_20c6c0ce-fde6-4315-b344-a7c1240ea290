# 是否多用户模式，如果启动 startup 脚本时使用了 -m 1 选择多用户模式，-m single 运行单用户模式，否则根据此处的参数选择模式
reader.app.secure=true

# 邀请码，如果启动 startup 脚本时使用了参数 -i 邀请码，则会覆盖此处
reader.app.inviteCode=

# 管理密码，如果启动 startup 脚本时使用了参数 -k 管理密码，则会覆盖此处
reader.app.secureKey=

# 书源代理可通过 header 设置

# 是否缓存章节内容
reader.app.cacheChapterContent=true

# 用户上限，免费版用户上限最大15
reader.app.userLimit=15

# 是否开启书源调试日志
reader.app.debugLog=false

# 自动清理不活跃用户，单位天，0为不清理，大于0的数字为清理多少天未登录用户
reader.app.autoClearInactiveUser=0

# mongodb数据备份，mongodb链接地址
reader.app.mongoUri=
# mongodb数据备份，mongodb数据库名
reader.app.mongoDbName=reader

# 书架自动更新间隔，单位分钟
reader.app.shelfUpdateInteval=10

# 远程webview接口地址，可通过部署 hectorqin/remote-webview 来设置，http://IP:8050
reader.app.remoteWebviewApi=

# 新用户默认是否启用 webdav
reader.app.defaultUserEnableWebdav=true
# 新用户是否默认启用 本地书仓
reader.app.defaultUserEnableLocalStore=true
# 新用户是否默认启用 书源编辑
reader.app.defaultUserEnableBookSource=true
# 新用户是否默认启用 RSS源编辑
reader.app.defaultUserEnableRssSource=true
# 新用户是否默认书源数量上限
reader.app.defaultUserBookSourceLimit=100
# 新用户是否默认书籍数量上限
reader.app.defaultUserBookLimit=200

# 是否自动备份用户数据
reader.app.autoBackupUserData=false

# reader服务监听端口
reader.server.port=8080
# reader接口目录
reader.server.contextPath=