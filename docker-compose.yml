version: '3.1'
services:
# reader 在线阅读
# 公开服务器(服务器位于日本)：[https://reader.nxnow.top](https://reader.nxnow.top) 测试账号/密码分别为guest/guest123，也可自行创建账号添加书源，不定期删除长期未登录账号(2周)
# 书源集合 : [https://legado.aoaostar.com/](https://legado.aoaostar.com/) 点击打开连接，添加远程书源即可
# 公众号汇总 : [https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MjM5MzMyMDgyMA==&action=getalbum&album_id=2397535253763801090#wechat_redirect](https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MjM5MzMyMDgyMA==&action=getalbum&album_id=2397535253763801090#wechat_redirect)
# 手动更新方式 : docker-compose pull && docker-compose up -d
  reader:
    #image: hectorqin/reader # 普通镜像
    image: hectorqin/reader:openj9-latest # Openj9镜像，arm64架构或小内存机器优先使用
    container_name: reader #容器名 可自行修改
    restart: always
    ports:
      - 4396:8080 #4396端口映射可自行修改,8080请勿修改
    volumes:
      - /home/<USER>/logs:/logs #log映射目录 /home/<USER>/logs 映射目录可自行修改
      - /home/<USER>/storage:/storage #数据映射目录 /home/<USER>/storage 映射目录可自行修改
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      #- READER_APP_USERLIMIT=50 #用户上限,默认且最大值为50
      - READER_APP_USERBOOKLIMIT=200 #用户书籍上限,默认200
      - READER_APP_CACHECHAPTERCONTENT=true #开启缓存章节内容
      - READER_APP_REMOTEWEBVIEWAPI=http://readerwebview:8050 #启用webview(若下方readerwebview容器不开启需注释此行
      # ↓多用户模式配置↓
      - READER_APP_SECURE=true #开启登录鉴权，开启后将支持多用户模式
      - READER_APP_SECUREKEY=adminpwd  #管理员密码  建议修改
      - READER_APP_INVITECODE=registercode #注册邀请码 建议修改,如不需要可注释或删除
# 如需支持webview书源，打开(占用较大，不需要可加 # 注释)
  readerwebview:
    image: hectorqin/remote-webview
    container_name: readerwebview
    restart: always
    environment:
      - TZ=Asia/Shanghai
# 自动更新docker镜像
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: always
    environment:
        - TZ=Asia/Shanghai
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: reader readerwebview watchtower --cleanup --schedule "0 0 4 * * *"
    # 仅更新reader与watchtower容器,如需其他自行添加 '容器名' ,如:reader watchtower nginx
    # --cleanup 更新后清理旧版本镜像
    # --schedule 自动检测更新 crontab定时(限定6位crontab) 此处代表凌晨4点整
volumes:
  reader:
  readerwebview: