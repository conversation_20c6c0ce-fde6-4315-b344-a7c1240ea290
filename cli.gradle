buildscript {
    ext.kotlin_version = '1.5.21'
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}
plugins {
    id 'org.springframework.boot' version '2.1.6.RELEASE'
    id 'java'
    id "org.jetbrains.kotlin.plugin.spring" version "1.3.61"
}

apply plugin: 'io.spring.dependency-management'
apply plugin: 'kotlin'

group = 'com.htmake'
version = '2.5.4'
sourceCompatibility = '1.8'

repositories {
    mavenCentral()
    maven { url "https://jitpack.io" }
    maven { url "https://gitlab.com/api/v4/projects/26729549/packages/maven" }
    google()
    jcenter()
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    // vertx
    implementation "io.vertx:vertx-core:3.8.1"
    implementation "io.vertx:vertx-lang-kotlin:3.8.1"
    implementation "io.vertx:vertx-lang-kotlin-coroutines:3.8.1"
    implementation 'io.vertx:vertx-web:3.8.1'
    implementation 'io.vertx:vertx-web-client:3.8.1'

    // json
    implementation "com.google.code.gson:gson:2.8.5"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:2.13.+"

    // log
    implementation "io.github.microutils:kotlin-logging:1.6.24"
    implementation "uk.org.lidalia:sysout-over-slf4j:1.0.2"

    implementation "com.google.guava:guava:28.0-jre"

    // 网络
    implementation "com.squareup.okhttp3:okhttp:4.9.1"
    implementation "com.squareup.okhttp3:logging-interceptor:4.1.0"
    // Retrofit
    implementation "com.squareup.retrofit2:retrofit:2.6.1"
    implementation "com.julienviet:retrofit-vertx:1.1.3"

    //JS rhino
    // implementation "com.github.gedoor:rhino-android:1.6"
    implementation(fileTree(dir: 'src/lib', include: ['rhino-*.jar']))

    // 规则相关
    implementation "org.jsoup:jsoup:1.14.1"
    implementation "cn.wanghaomiao:JsoupXpath:2.5.0"
    implementation "com.jayway.jsonpath:json-path:2.6.0"

    // xml
    // 弃用 xmlpull-1.1.4.0，因为它需要 Java9
    // implementation "org.xmlpull:xmlpull:1.1.4.0"
    implementation(fileTree(dir: 'src/lib', include: ['xmlpull-*.jar']))
    // implementation "com.github.stefanhaustein:kxml2:2.4.2"

    //加解密类库
    implementation "cn.hutool:hutool-crypto:5.8.0.M1"

    // 转换繁体
    // implementation "com.github.liuyueyi.quick-chinese-transfer:quick-transfer-core:0.2.1"
}
compileKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}
compileTestKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}
