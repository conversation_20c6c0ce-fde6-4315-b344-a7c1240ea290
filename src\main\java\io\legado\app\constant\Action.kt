package io.legado.app.constant

object Action {

    const val play = "play"
    const val stop = "stop"
    const val resume = "resume"
    const val pause = "pause"
    const val addTimer = "addTimer"
    const val setTimer = "setTimer"
    const val prevParagraph = "prevParagraph"
    const val nextParagraph = "nextParagraph"
    const val upTtsSpeechRate = "upTtsSpeechRate"
    const val adjustProgress = "adjustProgress"
    const val prev = "prev"
    const val next = "next"
    const val moveTo = "moveTo"
    const val init = "init"
}