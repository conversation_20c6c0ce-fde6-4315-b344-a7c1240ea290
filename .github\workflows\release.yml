name: Publish Docker Multi-Platform Images

on:
  # push:
  #   tags:
  #     - 'v**'
  workflow_dispatch:

jobs:
  docker:
    if: github.repository == 'hectorqin/reader'
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v2
      -
        name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: '14'
      -
        name: Build web
        run: cd web && npm install && npm run build
      -
        name: Setup Java
        uses: actions/setup-java@v2
        with:
          distribution: 'temurin'
          java-version: '8'
          cache: 'gradle'
      -
        name: Build Java
        run:
          mv ./web/dist ./src/main/resources/web && rm src/main/java/com/htmake/reader/ReaderUIApplication.kt && gradle -b cli.gradle assemble --info && mv ./build/libs/*.jar ./reader.jar
      -
        name: Docker meta
        id: meta
        uses: docker/metadata-action@v3
        with:
          # list of Docker images to use as base name for tags
          images: |
            hectorqin/reader-basic
          # generate Docker tags based on the following events/attributes
          tags: |
            type=semver,pattern={{version}}
            type=raw,value=latest,enable=${{ !contains(github.ref, 'beta') }}
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v1
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      -
        name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      -
        name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: .
          file: ./.github/workflows/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          platforms: |
            linux/amd64
            linux/arm64
            linux/arm/v6
            linux/arm/v7
            linux/386
            linux/ppc64le
            linux/s390x