<!--

Title:

     Mnemonic Character Entities For the Open eBook Publication
     Structure Version 1.2


Version:

     1.2


Revision:

     20020930-x  (supercedes 20020424-x)


Revision History Note:

     This revision, 20020930-x, which supercedes the prior revision
     20020424-x, updates: 1) an email address within this comment
     prologue, and 2) the Unicode version number referenced in various
     comments throughout this document. No changes whatsoever were
     made to the parsed content of this DTD fragment.


Previous Version:

     1.0.1 (Revision of 22-November-2000, "Character Entities for
            the Open eBook Publication Structure Version 1.0.1")


Authors: <AUTHORS>

          <PERSON><PERSON> <<EMAIL>>
          <PERSON> <<EMAIL>>
          <PERSON><PERSON><PERSON> <<EMAIL>>


     This Version 1.2 updated and edited by:

          <PERSON> <<EMAIL>>


Usage:

     <!ENTITY % OEBEntities
              PUBLIC "+//ISBN 0-9673008-1-9//DTD OEB 1.2 Entities//EN"
              "http://openebook.org/dtds/oeb-1.2/oeb12.ent">

     %OEBEntities;


Summary:

     This DTD fragment exactly duplicates, with some reorganization,
     correction, and reformatting of the descriptive text, the 253
     character entity declarations in the XHTML 1.1 DTD. Refer to:

          http://www.w3.org/TR/xhtml1/DTD/xhtml-lat1.ent
          http://www.w3.org/TR/xhtml1/DTD/xhtml-symbol.ent
          http://www.w3.org/TR/xhtml1/DTD/xhtml-special.ent


Relation to OEBPS Version 1.0.1:

     The 253 character entities declared herein include all 249 from
     Version 1.0.1 plus four of the five pre-defined XML 1.0 character
     entities of &amp;, &lt;, &gt;, &quot; (the fifth pre-defined XML
     character entity, &apos;, is one of the 249 character entities
     already declared in Version 1.0.1.)

     The five pre-defined XML 1.0 character entities are included for
     completeness and interoperability as recommended by W3C, and to
     follow XHTML practice. (Further information on the purpose and
     usage of these five pre-defined XML character entities, and the
     normative reference, is given in the Usage Note below.)


Relation to Unicode 3.2.0 and ISO/IEC 10646:

     The mnemonic character entities declared herein substitute for
     numeric character references, the numeric values for the
     associated characters specified by Unicode (in turn, the Unicode
     Character Data Set conforms with the ISO/IEC 10646 character set
     which XML 1.0 specifies.) The current version of Unicode is
     3.2.0. General information on Unicode, including information on
     the latest version, is found at

          http://www.unicode.org/

     In addition, Unicode has categorized the massive number of
     characters in its Character Database using two different systems:
     Character Blocks and Script Names. These two systems are used
     herein for general categorization of the 253 character entities.
     The text files listing the code points for these two systems are:

          http://www.unicode.org/Public/UNIDATA/Blocks.txt
          http://www.unicode.org/Public/UNIDATA/Scripts.txt


Tutorial Note to Document Authors: <AUTHORS>

     To insert the desired special character into the content of an
     OEBPS Document or Package file (which are XML documents), prefix
     the associated mnemonic character entity with the '&' character
     and terminate with the ';' character.

     Example: to insert the "em dash" character (which has the
     mnemonic 'mdash'), use &mdash; .

     If preferred, the character can instead be inserted using the
     direct (Unicode) numerical character reference, the codes of
     which are given herein (see the above note on Unicode.) So, for
     the "em dash" character one can use, instead of &mdash;, either
     the decimal &#8212; or the hexadecimal equivalent &#x2014; .

     Importantly note that within the content (PCDATA) of all OEBPS
     documents and package files, the special XML characters '&' and
     '<', when intended to be used literally, MUST be represented with
     the mnemonic character entities of &amp; and &lt; (or the numerical
     character entity equivalents), respectively. In addition, it is
     considered good practice to use the &gt; (or numerical equivalent)
     for the '>' symbol, although it is not necessary except in very
     unusual and rare circumstances. The two other special XML character
     entities, apostrophe (&apos;) and quote (&quot;), are only
     necessary within element attribute values to literally represent
     these characters, and for similar non-content purposes.

     (The normative reference on the five XML pre-defined mnemonic
     character entities is given in Sections 2.4 and 4.6 of the XML
     1.0 Specification, Second Edition:

          http://www.w3.org/TR/2000/REC-xml-20001006

     )


     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
       Portions (C) International Organization for
       Standardization 1986. Permission to copy in any
       form is granted for use with conforming SGML
       systems and applications as defined in ISO 8879,
       provided this notice is included in all copies.
     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+

-->

<!--

     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
       XML 1.0 Pre-Defined Character Entities
     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+

       Drawn From Unicode 3.2.0 Character Sets:

             Block Name(s):  Basic Latin              (U+0000 to U+007F)
            Script Name(s):  (none)

-->


<!ENTITY quot       "&#34;" ><!-- quotation mark
                                  APL quote
                                  ==================== U+0022 ISOnum -->

<!ENTITY amp    "&#38;#38;" ><!-- ampersand
                                  ==================== U+0026 ISOnum -->

<!ENTITY apos       "&#39;" ><!-- apostrophe mark
                                  ==================== U+0027 ISOnum -->

<!ENTITY lt     "&#38;#60;" ><!-- less-than sign
                                  ==================== U+003C ISOnum -->

<!ENTITY gt         "&#62;" ><!-- greater-than sign
                                  ==================== U+003E ISOnum -->


<!--

     +-+-+-+-+-+-+-+-+-+-+-+-+
       Extended Latin Script
     +-+-+-+-+-+-+-+-+-+-+-+-+

       Drawn From Unicode 3.2.0 Character Sets:

             Block Name(s):  Latin-1 Supplement       (U+0080 to U+00FF)
                             Latin Extended-A         (U+0100 to U+017F)
                             Latin Extended-B         (U+0180 to U+024F)
            Script Name(s):  Latin

-->


<!ENTITY ordf      "&#170;" ><!-- feminine ordinal indicator
                                  ==================== U+00AA ISOnum -->

<!ENTITY ordm      "&#186;" ><!-- masculine ordinal indicator
                                  ==================== U+00BA ISOnum -->

<!ENTITY Agrave    "&#192;" ><!-- Latin capital letter A with grave
                                  Latin capital letter A grave
                                  =================== U+00C0 ISOlat1 -->

<!ENTITY Aacute    "&#193;" ><!-- Latin capital letter A with acute
                                  =================== U+00C1 ISOlat1 -->

<!ENTITY Acirc     "&#194;" ><!-- Latin capital letter A with circumflex
                                  =================== U+00C2 ISOlat1 -->

<!ENTITY Atilde    "&#195;" ><!-- Latin capital letter A with tilde
                                  =================== U+00C3 ISOlat1 -->

<!ENTITY Auml      "&#196;" ><!-- Latin capital letter A with diaeresis
                                  =================== U+00C4 ISOlat1 -->

<!ENTITY Aring     "&#197;" ><!-- Latin capital letter A with ring above
                                  Latin capital letter A ring
                                  =================== U+00C5 ISOlat1 -->

<!ENTITY AElig     "&#198;" ><!-- Latin capital letter AE
                                  Latin capital ligature AE
                                  =================== U+00C6 ISOlat1 -->

<!ENTITY Ccedil    "&#199;" ><!-- Latin capital letter C with cedilla
                                  =================== U+00C7 ISOlat1 -->

<!ENTITY Egrave    "&#200;" ><!-- Latin capital letter E with grave
                                  =================== U+00C8 ISOlat1 -->

<!ENTITY Eacute    "&#201;" ><!-- Latin capital letter E with acute
                                  =================== U+00C9 ISOlat1 -->

<!ENTITY Ecirc     "&#202;" ><!-- Latin capital letter E with circumflex
                                  =================== U+00CA ISOlat1 -->

<!ENTITY Euml      "&#203;" ><!-- Latin capital letter E with diaeresis
                                  =================== U+00CB ISOlat1 -->

<!ENTITY Igrave    "&#204;" ><!-- Latin capital letter I with grave
                                  =================== U+00CC ISOlat1 -->

<!ENTITY Iacute    "&#205;" ><!-- Latin capital letter I with acute
                                  =================== U+00CD ISOlat1 -->

<!ENTITY Icirc     "&#206;" ><!-- Latin capital letter I with circumflex
                                  =================== U+00CE ISOlat1 -->

<!ENTITY Iuml      "&#207;" ><!-- Latin capital letter I with diaeresis
                                  =================== U+00CF ISOlat1 -->

<!ENTITY ETH       "&#208;" ><!-- Latin capital letter ETH
                                  =================== U+00D0 ISOlat1 -->

<!ENTITY Ntilde    "&#209;" ><!-- Latin capital letter N with tilde
                                  =================== U+00D1 ISOlat1 -->

<!ENTITY Ograve    "&#210;" ><!-- Latin capital letter O with grave
                                  =================== U+00D2 ISOlat1 -->

<!ENTITY Oacute    "&#211;" ><!-- Latin capital letter O with acute
                                  =================== U+00D3 ISOlat1 -->

<!ENTITY Ocirc     "&#212;" ><!-- Latin capital letter O with circumflex
                                  =================== U+00D4 ISOlat1 -->

<!ENTITY Otilde    "&#213;" ><!-- Latin capital letter O with tilde
                                  =================== U+00D5 ISOlat1 -->

<!ENTITY Ouml      "&#214;" ><!-- Latin capital letter O with diaeresis
                                  =================== U+00D6 ISOlat1 -->

<!ENTITY Oslash    "&#216;" ><!-- Latin capital letter O with stroke
                                  Latin capital letter O slash
                                  =================== U+00D8 ISOlat1 -->

<!ENTITY Ugrave    "&#217;" ><!-- Latin capital letter U with grave
                                  =================== U+00D9 ISOlat1 -->

<!ENTITY Uacute    "&#218;" ><!-- Latin capital letter U with acute
                                  =================== U+00DA ISOlat1 -->

<!ENTITY Ucirc     "&#219;" ><!-- Latin capital letter U with circumflex
                                  =================== U+00DB ISOlat1 -->

<!ENTITY Uuml      "&#220;" ><!-- Latin capital letter U with diaeresis
                                  =================== U+00DC ISOlat1 -->

<!ENTITY Yacute    "&#221;" ><!-- Latin capital letter Y with acute
                                  =================== U+00DD ISOlat1 -->

<!ENTITY THORN     "&#222;" ><!-- Latin capital letter THORN
                                  =================== U+00DE ISOlat1 -->

<!ENTITY szlig     "&#223;" ><!-- Latin small letter sharp s
                                  ess-zed
                                  =================== U+00DF ISOlat1 -->

<!ENTITY agrave    "&#224;" ><!-- Latin small letter a with grave
                                  Latin small letter a grave
                                  =================== U+00E0 ISOlat1 -->

<!ENTITY aacute    "&#225;" ><!-- Latin small letter a with acute
                                  =================== U+00E1 ISOlat1 -->

<!ENTITY acirc     "&#226;" ><!-- Latin small letter a with circumflex
                                  =================== U+00E2 ISOlat1 -->

<!ENTITY atilde    "&#227;" ><!-- Latin small letter a with tilde
                                  =================== U+00E3 ISOlat1 -->

<!ENTITY auml      "&#228;" ><!-- Latin small letter a with diaeresis
                                  =================== U+00E4 ISOlat1 -->

<!ENTITY aring     "&#229;" ><!-- Latin small letter a with ring above
                                  Latin small letter a ring
                                  =================== U+00E5 ISOlat1 -->

<!ENTITY aelig     "&#230;" ><!-- Latin small letter ae
                                  Latin small ligature ae
                                  =================== U+00E6 ISOlat1 -->

<!ENTITY ccedil    "&#231;" ><!-- Latin small letter c with cedilla
                                  =================== U+00E7 ISOlat1 -->

<!ENTITY egrave    "&#232;" ><!-- Latin small letter e with grave
                                  =================== U+00E8 ISOlat1 -->

<!ENTITY eacute    "&#233;" ><!-- Latin small letter e with acute
                                  =================== U+00E9 ISOlat1 -->

<!ENTITY ecirc     "&#234;" ><!-- Latin small letter e with circumflex
                                  =================== U+00EA ISOlat1 -->

<!ENTITY euml      "&#235;" ><!-- Latin small letter e with diaeresis
                                  =================== U+00EB ISOlat1 -->

<!ENTITY igrave    "&#236;" ><!-- Latin small letter i with grave
                                  =================== U+00EC ISOlat1 -->

<!ENTITY iacute    "&#237;" ><!-- Latin small letter i with acute
                                  =================== U+00ED ISOlat1 -->

<!ENTITY icirc     "&#238;" ><!-- Latin small letter i with circumflex
                                  =================== U+00EE ISOlat1 -->

<!ENTITY iuml      "&#239;" ><!-- Latin small letter i with diaeresis
                                  =================== U+00EF ISOlat1 -->

<!ENTITY eth       "&#240;" ><!-- Latin small letter eth
                                  =================== U+00F0 ISOlat1 -->

<!ENTITY ntilde    "&#241;" ><!-- Latin small letter n with tilde
                                  =================== U+00F1 ISOlat1 -->

<!ENTITY ograve    "&#242;" ><!-- Latin small letter o with grave
                                  =================== U+00F2 ISOlat1 -->

<!ENTITY oacute    "&#243;" ><!-- Latin small letter o with acute
                                  =================== U+00F3 ISOlat1 -->

<!ENTITY ocirc     "&#244;" ><!-- Latin small letter o with circumflex
                                  =================== U+00F4 ISOlat1 -->

<!ENTITY otilde    "&#245;" ><!-- Latin small letter o with tilde
                                  =================== U+00F5 ISOlat1 -->

<!ENTITY ouml      "&#246;" ><!-- Latin small letter o with diaeresis
                                  =================== U+00F6 ISOlat1 -->

<!ENTITY oslash    "&#248;" ><!-- Latin small letter o with stroke
                                  Latin small letter o slash
                                  =================== U+00F8 ISOlat1 -->

<!ENTITY ugrave    "&#249;" ><!-- Latin small letter u with grave
                                  =================== U+00F9 ISOlat1 -->

<!ENTITY uacute    "&#250;" ><!-- Latin small letter u with acute
                                  =================== U+00FA ISOlat1 -->

<!ENTITY ucirc     "&#251;" ><!-- Latin small letter u with circumflex
                                  =================== U+00FB ISOlat1 -->

<!ENTITY uuml      "&#252;" ><!-- Latin small letter u with diaeresis
                                  =================== U+00FC ISOlat1 -->

<!ENTITY yacute    "&#253;" ><!-- Latin small letter y with acute
                                  =================== U+00FD ISOlat1 -->

<!ENTITY thorn     "&#254;" ><!-- Latin small letter thorn with
                                  =================== U+00FE ISOlat1 -->

<!ENTITY yuml      "&#255;" ><!-- Latin small letter y with diaeresis
                                  =================== U+00FF ISOlat1 -->

<!ENTITY OElig     "&#338;" ><!-- Latin capital ligature OE
                                  =================== U+0152 ISOlat2 -->

<!ENTITY oelig     "&#339;" ><!-- Latin small ligature oe
                                  =================== U+0153 ISOlat2 -->

<!ENTITY Scaron    "&#352;" ><!-- Latin capital letter S with caron
                                  =================== U+0160 ISOlat2 -->

<!ENTITY scaron    "&#353;" ><!-- Latin small letter s with caron
                                  =================== U+0161 ISOlat2 -->

<!ENTITY Yuml      "&#376;" ><!-- Latin capital letter Y with diaeresis
                                  =================== U+0178 ISOlat2 -->

<!ENTITY fnof      "&#402;" ><!-- Latin small f with hook
                                  function
                                  florin
                                  =================== U+0192 ISOtech -->


<!--

     +-+-+-+-+-+-+-+
       Greek Script
     +-+-+-+-+-+-+-+

       Drawn From Unicode 3.2.0 Character Sets:

             Block Name(s):  Greek                    (U+0370 to U+03FF)
            Script Name(s):  Greek

-->


<!ENTITY Alpha     "&#913;" ><!-- Greek capital letter alpha
                                  =========================== U+0391 -->

<!ENTITY Beta      "&#914;" ><!-- Greek capital letter beta
                                  =========================== U+0392 -->

<!ENTITY Gamma     "&#915;" ><!-- Greek capital letter gamma
                                  =================== U+0393 ISOgrk3 -->

<!ENTITY Delta     "&#916;" ><!-- Greek capital letter delta
                                  =================== U+0394 ISOgrk3 -->

<!ENTITY Epsilon   "&#917;" ><!-- Greek capital letter epsilon
                                  =========================== U+0395 -->

<!ENTITY Zeta      "&#918;" ><!-- Greek capital letter zeta
                                  =========================== U+0396 -->

<!ENTITY Eta       "&#919;" ><!-- Greek capital letter eta
                                  =========================== U+0397 -->

<!ENTITY Theta     "&#920;" ><!-- Greek capital letter theta
                                  =================== U+0398 ISOgrk3 -->

<!ENTITY Iota      "&#921;" ><!-- Greek capital letter iota
                                  =========================== U+0399 -->

<!ENTITY Kappa     "&#922;" ><!-- Greek capital letter kappa
                                  =========================== U+039A -->

<!ENTITY Lambda    "&#923;" ><!-- Greek capital letter lambda
                                  =================== U+039B ISOgrk3 -->

<!ENTITY Mu        "&#924;" ><!-- Greek capital letter mu
                                  =========================== U+039C -->

<!ENTITY Nu        "&#925;" ><!-- Greek capital letter nu
                                  =========================== U+039D -->

<!ENTITY Xi        "&#926;" ><!-- Greek capital letter xi
                                  =================== U+039E ISOgrk3 -->

<!ENTITY Omicron   "&#927;" ><!-- Greek capital letter omicron
                                  =========================== U+039F -->

<!ENTITY Pi        "&#928;" ><!-- Greek capital letter pi
                                  =================== U+03A0 ISOgrk3 -->

<!ENTITY Rho       "&#929;" ><!-- Greek capital letter rho
                                  =========================== U+03A1 -->

<!ENTITY Sigma     "&#931;" ><!-- Greek capital letter sigma
                                  =================== U+03A3 ISOgrk3 -->

<!ENTITY Tau       "&#932;" ><!-- Greek capital letter tau
                                  =========================== U+03A4 -->

<!ENTITY Upsilon   "&#933;" ><!-- Greek capital letter upsilon
                                  =================== U+03A5 ISOgrk3 -->

<!ENTITY Phi       "&#934;" ><!-- Greek capital letter phi
                                  =================== U+03A6 ISOgrk3 -->

<!ENTITY Chi       "&#935;" ><!-- Greek capital letter chi
                                  =========================== U+03A7 -->

<!ENTITY Psi       "&#936;" ><!-- Greek capital letter psi
                                  =================== U+03A8 ISOgrk3 -->

<!ENTITY Omega     "&#937;" ><!-- Greek capital letter omega
                                  =================== U+03A9 ISOgrk3 -->

<!ENTITY alpha     "&#945;" ><!-- Greek small letter alpha
                                  =================== U+03B1 ISOgrk3 -->

<!ENTITY beta      "&#946;" ><!-- Greek small letter beta
                                  =================== U+03B2 ISOgrk3 -->

<!ENTITY gamma     "&#947;" ><!-- Greek small letter gamma
                                  =================== U+03B3 ISOgrk3 -->

<!ENTITY delta     "&#948;" ><!-- Greek small letter delta
                                  =================== U+03B4 ISOgrk3 -->

<!ENTITY epsilon   "&#949;" ><!-- Greek small letter epsilon
                                  =================== U+03B5 ISOgrk3 -->

<!ENTITY zeta      "&#950;" ><!-- Greek small letter zeta
                                  =================== U+03B6 ISOgrk3 -->

<!ENTITY eta       "&#951;" ><!-- Greek small letter eta
                                  =================== U+03B7 ISOgrk3 -->

<!ENTITY theta     "&#952;" ><!-- Greek small letter theta
                                  =================== U+03B8 ISOgrk3 -->

<!ENTITY iota      "&#953;" ><!-- Greek small letter iota
                                  =================== U+03B9 ISOgrk3 -->

<!ENTITY kappa     "&#954;" ><!-- Greek small letter kappa
                                  =================== U+03BA ISOgrk3 -->

<!ENTITY lambda    "&#955;" ><!-- Greek small letter lambda
                                  =================== U+03BB ISOgrk3 -->

<!ENTITY mu        "&#956;" ><!-- Greek small letter mu
                                  =================== U+03BC ISOgrk3 -->

<!ENTITY nu        "&#957;" ><!-- Greek small letter nu
                                  =================== U+03BD ISOgrk3 -->

<!ENTITY xi        "&#958;" ><!-- Greek small letter xi
                                  =================== U+03BE ISOgrk3 -->

<!ENTITY omicron   "&#959;" ><!-- Greek small letter omicron
                                  ======================= U+03BF NEW -->

<!ENTITY pi        "&#960;" ><!-- Greek small letter pi
                                  =================== U+03C0 ISOgrk3 -->

<!ENTITY rho       "&#961;" ><!-- Greek small letter rho
                                  =================== U+03C1 ISOgrk3 -->

<!ENTITY sigmaf    "&#962;" ><!-- Greek small letter final sigma
                                  =================== U+03C2 ISOgrk3 -->

<!ENTITY sigma     "&#963;" ><!-- Greek small letter sigma
                                  =================== U+03C3 ISOgrk3 -->

<!ENTITY tau       "&#964;" ><!-- Greek small letter tau
                                  =================== U+03C4 ISOgrk3 -->

<!ENTITY upsilon   "&#965;" ><!-- Greek small letter upsilon
                                  =================== U+03C5 ISOgrk3 -->

<!ENTITY phi       "&#966;" ><!-- Greek small letter phi
                                  =================== U+03C6 ISOgrk3 -->

<!ENTITY chi       "&#967;" ><!-- Greek small letter chi
                                  =================== U+03C7 ISOgrk3 -->

<!ENTITY psi       "&#968;" ><!-- Greek small letter psi
                                  =================== U+03C8 ISOgrk3 -->

<!ENTITY omega     "&#969;" ><!-- Greek small letter omega
                                  =================== U+03C9 ISOgrk3 -->

<!ENTITY thetasym  "&#977;" ><!-- Greek small letter theta symbol
                                  ======================= U+03D1 NEW -->

<!ENTITY upsih     "&#978;" ><!-- Greek upsilon with hook symbol
                                  ======================= U+03D2 NEW -->

<!ENTITY piv       "&#982;" ><!-- Greek pi symbol
                                  =================== U+03D6 ISOgrk3 -->


<!--

     +-+-+-+-+-+-+-+-+-+-+-+
       General Punctuation
     +-+-+-+-+-+-+-+-+-+-+-+

       Drawn From Unicode 3.2.0 Character Sets:

             Block Name(s):  General Punctuation      (U+2000 to U+206F)
            Script Name(s):  (none)

-->


<!ENTITY ensp     "&#8194;" ><!-- en space
                                  ==================== U+2002 ISOpub -->

<!ENTITY emsp     "&#8195;" ><!-- em space
                                  ==================== U+2003 ISOpub -->

<!ENTITY thinsp   "&#8201;" ><!-- thin space
                                  ==================== U+2009 ISOpub -->

<!ENTITY zwnj     "&#8204;" ><!-- zero width non-joiner
                                  ============== U+200C NEW RFC 2070 -->

<!ENTITY zwj      "&#8205;" ><!-- zero width joiner
                                  ============== U+200D NEW RFC 2070 -->

<!ENTITY lrm      "&#8206;" ><!-- left-to-right mark
                                  ============== U+200E NEW RFC 2070 -->

<!ENTITY rlm      "&#8207;" ><!-- right-to-left mark
                                  ============== U+200F NEW RFC 2070 -->

<!ENTITY ndash    "&#8211;" ><!-- en dash
                                  ==================== U+2013 ISOpub -->

<!ENTITY mdash    "&#8212;" ><!-- em dash
                                  ==================== U+2014 ISOpub -->

<!ENTITY lsquo    "&#8216;" ><!-- left single quotation mark
                                  ==================== U+2018 ISOnum -->

<!ENTITY rsquo    "&#8217;" ><!-- right single quotation mark
                                  ==================== U+2019 ISOnum -->

<!ENTITY sbquo    "&#8218;" ><!-- single low-9 quotation mark
                                  ======================= U+201A NEW -->

<!ENTITY ldquo    "&#8220;" ><!-- left double quotation mark
                                  ==================== U+201C ISOnum -->

<!ENTITY rdquo    "&#8221;" ><!-- right double quotation mark
                                  ==================== U+201D ISOnum -->

<!ENTITY bdquo    "&#8222;" ><!-- double low-9 quotation mark
                                  ======================= U+201E NEW -->

<!ENTITY dagger   "&#8224;" ><!-- dagger
                                  ==================== U+2020 ISOpub -->

<!ENTITY Dagger   "&#8225;" ><!-- double dagger
                                  ==================== U+2021 ISOpub -->

<!ENTITY bull     "&#8226;" ><!-- bullet
                                  black small circle
                                  ==================== U+2022 ISOpub -->
                             <!-- bullet is NOT the same as U+2219,
                                 'bullet operator' -->

<!ENTITY hellip   "&#8230;" ><!-- horizontal ellipsis
                                  three dot leader
                                  ==================== U+2026 ISOpub -->

<!ENTITY permil   "&#8240;" ><!-- per mille sign
                                  =================== U+2030 ISOtech -->

<!ENTITY prime    "&#8242;" ><!-- prime
                                  minutes
                                  feet
                                  =================== U+2032 ISOtech -->

<!ENTITY Prime    "&#8243;" ><!-- double prime
                                  seconds
                                  inches
                                  =================== U+2033 ISOtech -->

<!ENTITY lsaquo   "&#8249;" ><!-- single left-pointing angle quotation
                                       mark
                                  ============== U+2039 ISO proposed -->

<!ENTITY rsaquo   "&#8250;" ><!-- single right-pointing angle quotation
                                  ============== U+203A ISO proposed -->

<!ENTITY oline    "&#8254;" ><!-- overline
                                  spacing overscore
                                  ======================= U+203E NEW -->

<!ENTITY frasl    "&#8260;" ><!-- fraction slash
                                  ======================= U+2044 NEW -->


<!--

     +-+-+-+-+-+-+-+-+-+-+
       Spacing Modifiers
     +-+-+-+-+-+-+-+-+-+-+

       Drawn From Unicode 3.2.0 Character Sets:

             Block Name(s):  Spacing Modifier Letters (U+0280 to U+02FF)
            Script Name(s):  (none)

       Note: The Spacing Modifier Letters are an unusual class of
             characters. They are an assorted collection of small signs
             used to indicate modifications of the preceding or
             following character, and sometimes to be an independent
             character. They differ from diacritical marks in that they
             are treated as free-standing, independent characters, which
             form part of the word and do not break up the word. They
             have the "letter" property. Most of the characters are
             phonetic modifiers. For further information, refer to
             Section 7.8 of the Unicode 3.2 manual, an online version is
             at http://www.unicode.org/unicode/uni2book/ch07.pdf .

-->


<!ENTITY circ      "&#710;" ><!-- modifier letter circumflex accent
                                  ==================== U+02C6 ISOpub -->

<!ENTITY tilde     "&#732;" ><!-- small tilde
                                  ==================== U+02DC ISOdia -->


<!--

     +-+-+-+-+-+-+-+-+-+
       Various Symbols
     +-+-+-+-+-+-+-+-+-+

       Drawn From Unicode 3.2.0 Character Sets:

             Block Name(s):  Latin-1 Supplement       (U+0080 to U+00FF)
                             Currency Symbols         (U+20A0 to U+20CF)
                             Letterlike Symbols       (U+2100 to U+214F)
                             Arrows                   (U+2190 to U+21FF)
                             Mathematical Operators   (U+2200 to U+22FF)
                             Miscellaneous Technical  (U+2300 to U+23FF)
                             Geometric Shapes         (U+25A0 to U+25FF)
                             Miscellaneous Symbols    (U+2600 to U+26FF)
            Script Name(s):  (none, except Greek for "micro", U+00B5)

-->


<!ENTITY nbsp      "&#160;" ><!-- no-break space
                                  non-breaking space
                                  ==================== U+00A0 ISOnum -->

<!ENTITY iexcl     "&#161;" ><!-- inverted exclamation mark
                                  ==================== U+00A1 ISOnum -->

<!ENTITY cent      "&#162;" ><!-- cent sign
                                  ==================== U+00A2 ISOnum -->

<!ENTITY pound     "&#163;" ><!-- pound sign
                                  ==================== U+00A3 ISOnum -->

<!ENTITY curren    "&#164;" ><!-- currency sign
                                  ==================== U+00A4 ISOnum -->

<!ENTITY yen       "&#165;" ><!-- yen sign
                                  yuan sign
                                  ==================== U+00A5 ISOnum -->

<!ENTITY brvbar    "&#166;" ><!-- broken bar
                                  broken vertical bar
                                  ==================== U+00A6 ISOnum -->

<!ENTITY sect      "&#167;" ><!-- section sign
                                  ==================== U+00A7 ISOnum -->

<!ENTITY uml       "&#168;" ><!-- diaeresis
                                  spacing diaeresis
                                  ==================== U+00A8 ISOdia -->

<!ENTITY copy      "&#169;" ><!-- copyright sign
                                  ==================== U+00A9 ISOnum -->

<!ENTITY laquo     "&#171;" ><!-- left-pointing double angle quotation
                                       mark
                                  left pointing guillemet
                                  ==================== U+00AB ISOnum -->

<!ENTITY not       "&#172;" ><!-- not sign
                                  ==================== U+00AC ISOnum -->

<!ENTITY shy       "&#173;" ><!-- soft hyphen
                                  discretionary hyphen
                                  ==================== U+00AD ISOnum -->

<!ENTITY reg       "&#174;" ><!-- registered sign
                                  registered trade mark sign
                                  ==================== U+00AE ISOnum -->

<!ENTITY macr      "&#175;" ><!-- macron
                                  spacing macron
                                  overline
                                  APL overbar
                                  ==================== U+00AF ISOdia -->

<!ENTITY deg       "&#176;" ><!-- degree sign
                                  ==================== U+00B0 ISOnum -->

<!ENTITY plusmn    "&#177;" ><!-- plus-minus sign
                                  plus-or-minus sign
                                  ==================== U+00B1 ISOnum -->

<!ENTITY sup2      "&#178;" ><!-- superscript two
                                  superscript digit two
                                  squared
                                  ==================== U+00B2 ISOnum -->

<!ENTITY sup3      "&#179;" ><!-- superscript three
                                  superscript digit three
                                  cubed
                                  ==================== U+00B3 ISOnum -->

<!ENTITY acute     "&#180;" ><!-- acute accent
                                  spacing acute
                                  ==================== U+00B4 ISOdia -->

<!ENTITY micro     "&#181;" ><!-- micro sign
                                  ==================== U+00B5 ISOnum -->

<!ENTITY para      "&#182;" ><!-- pilcrow sign
                                  paragraph sign
                                  ==================== U+00B6 ISOnum -->

<!ENTITY middot    "&#183;" ><!-- middle dot
                                  Georgian comma
                                  Greek middle dot
                                  ==================== U+00B7 ISOnum -->

<!ENTITY cedil     "&#184;" ><!-- cedilla
                                  spacing cedilla
                                  ==================== U+00B8 ISOdia -->

<!ENTITY sup1      "&#185;" ><!-- superscript one
                                  superscript digit one
                                  ==================== U+00B9 ISOnum -->

<!ENTITY raquo     "&#187;" ><!-- right-pointing double angle quotation
                                       mark
                                  right pointing guillemet
                                  ==================== U+00BB ISOnum -->

<!ENTITY frac14    "&#188;" ><!-- vulgar fraction one quarter
                                  fraction one quarter
                                  ==================== U+00BC ISOnum -->

<!ENTITY frac12    "&#189;" ><!-- vulgar fraction one half
                                  fraction one half
                                  ==================== U+00BD ISOnum -->

<!ENTITY frac34    "&#190;" ><!-- vulgar fraction three quarters
                                  fraction three quarters
                                  ==================== U+00BE ISOnum -->

<!ENTITY iquest    "&#191;" ><!-- inverted question mark
                                  turned question mark
                                  ==================== U+00BF ISOnum -->

<!ENTITY times     "&#215;" ><!-- multiplication sign
                                  ==================== U+00D7 ISOnum -->

<!ENTITY divide    "&#247;" ><!-- division sign
                                  ==================== U+00F7 ISOnum -->

<!ENTITY euro     "&#8364;" ><!-- euro sign
                                  ======================= U+20AC NEW -->

<!ENTITY image    "&#8465;" ><!-- blackletter capital I
                                  imaginary part
                                  =================== U+2111 ISOamso -->

<!ENTITY weierp   "&#8472;" ><!-- script capital P
                                  power set
                                  Weierstrass p
                                  =================== U+2118 ISOamso -->

<!ENTITY real     "&#8476;" ><!-- blackletter capital R
                                  real part symbol
                                  =================== U+211C ISOamso -->

<!ENTITY trade    "&#8482;" ><!-- trade mark sign
                                  ==================== U+2122 ISOnum -->

<!ENTITY alefsym  "&#8501;" ><!-- alef symbol
                                  first transfinite cardinal
                                  ======================= U+2135 NEW -->
                             <!-- alef symbol is NOT the same as
                                  U+05D0, 'Hebrew letter alef',
                                  although the same glyph could be
                                  used to represent both -->

<!ENTITY larr     "&#8592;" ><!-- leftwards arrow
                                  ==================== U+2190 ISOnum -->

<!ENTITY uarr     "&#8593;" ><!-- upwards arrow
                                  ==================== U+2191 ISOnum -->

<!ENTITY rarr     "&#8594;" ><!-- rightwards arrow
                                  ==================== U+2192 ISOnum -->

<!ENTITY darr     "&#8595;" ><!-- downwards arrow
                                  ==================== U+2193 ISOnum -->

<!ENTITY harr     "&#8596;" ><!-- left right arrow
                                  =================== U+2194 ISOamsa -->

<!ENTITY crarr    "&#8629;" ><!-- downwards arrow with corner leftwards
                                  carriage return
                                  ======================= U+21B5 NEW -->

<!ENTITY lArr     "&#8656;" ><!-- leftwards double arrow
                                  =================== U+21D0 ISOtech -->
                             <!-- Unicode does not say that lArr is
                                  the same as the 'is implied by'
                                  arrow, but also does not have any
                                  other character for that function.
                                  As ISOtech suggests, lArr can be
                                  used for 'is implied by'. -->

<!ENTITY uArr     "&#8657;" ><!-- upwards double arrow
                                  =================== U+21D1 ISOamsa -->

<!ENTITY rArr     "&#8658;" ><!-- rightwards double arrow
                                  =================== U+21D2 ISOtech -->
                             <!-- Unicode does not say that rArr is
                                  the same as the 'implies' arrow,
                                  but also does not have any other
                                  character for that function. As
                                  ISOtech suggests, rArr can be used
                                  for 'implies'. -->

<!ENTITY dArr     "&#8659;" ><!-- downwards double arrow
                                  =================== U+21D3 ISOamsa -->

<!ENTITY hArr     "&#8660;" ><!-- left right double arrow
                                  =================== U+21D4 ISOamsa -->

<!ENTITY forall   "&#8704;" ><!-- for all
                                  =================== U+2200 ISOtech -->

<!ENTITY part     "&#8706;" ><!-- partial differential
                                  =================== U+2202 ISOtech -->

<!ENTITY exist    "&#8707;" ><!-- there exists
                                  =================== U+2203 ISOtech -->

<!ENTITY empty    "&#8709;" ><!-- empty set
                                  null set
                                  diameter
                                  =================== U+2205 ISOamso -->

<!ENTITY nabla    "&#8711;" ><!-- nabla
                                  backward difference
                                  =================== U+2207 ISOtech -->

<!ENTITY isin     "&#8712;" ><!-- element of
                                  =================== U+2208 ISOtech -->

<!ENTITY notin    "&#8713;" ><!-- not an element of
                                  =================== U+2209 ISOtech -->

<!ENTITY ni       "&#8715;" ><!-- contains as member
                                  =================== U+220B ISOtech -->

<!ENTITY prod     "&#8719;" ><!-- n-ary product
                                  product sign
                                  =================== U+220F ISOamsb -->
                             <!-- prod is NOT the same character as
                                  U+03A0, 'Greek capital letter pi',
                                  although the same glyph could be
                                  used to represent both -->

<!ENTITY sum      "&#8721;" ><!-- n-ary summation
                                  =================== U+2211 ISOamsb -->
                             <!-- sum is NOT the same character as
                                  U+03A3, 'Greek capital letter sigma',
                                  although the same glyph could be
                                  used to represent both -->

<!ENTITY minus    "&#8722;" ><!-- minus sign
                                  =================== U+2212 ISOtech -->

<!ENTITY lowast   "&#8727;" ><!-- asterisk operator
                                  =================== U+2217 ISOtech -->

<!ENTITY radic    "&#8730;" ><!-- square root
                                  radical sign
                                  =================== U+221A ISOtech -->

<!ENTITY prop     "&#8733;" ><!-- proportional to
                                  =================== U+221D ISOtech -->

<!ENTITY infin    "&#8734;" ><!-- infinity
                                  =================== U+221E ISOtech -->

<!ENTITY ang      "&#8736;" ><!-- angle
                                  =================== U+2220 ISOamso -->

<!ENTITY and      "&#8743;" ><!-- logical and
                                  wedge
                                  =================== U+2227 ISOtech -->

<!ENTITY or       "&#8744;" ><!-- logical or
                                  vee
                                  =================== U+2228 ISOtech -->

<!ENTITY cap      "&#8745;" ><!-- intersection
                                  cap
                                  =================== U+2229 ISOtech -->

<!ENTITY cup      "&#8746;" ><!-- union
                                  cup
                                  =================== U+222A ISOtech -->

<!ENTITY int      "&#8747;" ><!-- integral
                                  =================== U+222B ISOtech -->

<!ENTITY there4   "&#8756;" ><!-- therefore
                                  =================== U+2234 ISOtech -->

<!ENTITY sim      "&#8764;" ><!-- tilde operator
                                  varies with
                                  similar to
                                  =================== U+223C ISOtech -->
                             <!-- tilde operator is NOT the same
                                  character as U+007E, 'tilde',
                                  although the same glyph could be
                                  used to represent both -->

<!ENTITY cong     "&#8773;" ><!-- approximately equal to
                                  =================== U+2245 ISOtech -->

<!ENTITY asymp    "&#8776;" ><!-- almost equal to
                                  asymptotic to
                                  =================== U+2248 ISOamsr -->

<!ENTITY ne       "&#8800;" ><!-- not equal to
                                  =================== U+2260 ISOtech -->

<!ENTITY equiv    "&#8801;" ><!-- identical to
                                  =================== U+2261 ISOtech -->

<!ENTITY le       "&#8804;" ><!-- less-than or equal to
                                  =================== U+2264 ISOtech -->

<!ENTITY ge       "&#8805;" ><!-- greater-than or equal to
                                  =================== U+2265 ISOtech -->

<!ENTITY sub      "&#8834;" ><!-- subset of
                                  =================== U+2282 ISOtech -->

<!ENTITY sup      "&#8835;" ><!-- superset of
                                  =================== U+2283 ISOtech -->

<!ENTITY nsub     "&#8836;" ><!-- not a subset of
                                  =================== U+2284 ISOamsn -->

<!ENTITY sube     "&#8838;" ><!-- subset of or equal to
                                  =================== U+2286 ISOtech -->

<!ENTITY supe     "&#8839;" ><!-- superset of or equal to
                                  =================== U+2287 ISOtech -->

<!ENTITY oplus    "&#8853;" ><!-- circled plus
                                  direct sum
                                  =================== U+2295 ISOamsb -->

<!ENTITY otimes   "&#8855;" ><!-- circled times
                                  vector product
                                  =================== U+2297 ISOamsb -->

<!ENTITY perp     "&#8869;" ><!-- up tack
                                  orthogonal to
                                  perpendicular
                                  =================== U+22A5 ISOtech -->

<!ENTITY sdot     "&#8901;" ><!-- dot operator
                                  =================== U+22C5 ISOamsb -->
                             <!-- dot operator is NOT the same
                                  character as U+00B7, 'middle dot' -->

<!ENTITY lceil    "&#8968;" ><!-- left ceiling
                                  APL upstile
                                  =================== U+2308 ISOamsc -->

<!ENTITY rceil    "&#8969;" ><!-- right ceiling
                                  =================== U+2309 ISOamsc -->

<!ENTITY lfloor   "&#8970;" ><!-- left floor
                                  APL downstile
                                  =================== U+230A ISOamsc -->

<!ENTITY rfloor   "&#8971;" ><!-- right floor
                                  =================== U+230B ISOamsc -->

<!ENTITY lang     "&#9001;" ><!-- left-pointing angle bracket
                                  bra
                                  =================== U+2329 ISOtech -->
                             <!-- lang is NOT the same character as
                                  U+003C, 'less than', or U+2039,
                                 'single left-pointing angle quotation
                                  mark' -->

<!ENTITY rang     "&#9002;" ><!-- right-pointing angle bracket
                                  ket
                                  =================== U+232A ISOtech -->
                             <!-- rang is NOT the same character as
                                  U+003E, 'greater than', or U+203A,
                                 'single right-pointing angle quotation
                                  mark' -->

<!ENTITY loz      "&#9674;" ><!-- lozenge
                                  ==================== U+25CA ISOpub -->

<!ENTITY spades   "&#9824;" ><!-- black spade suit
                                  ==================== U+2660 ISOpub -->

<!ENTITY clubs    "&#9827;" ><!-- black club suit
                                  shamrock
                                  ==================== U+2663 ISOpub -->

<!ENTITY hearts   "&#9829;" ><!-- black heart suit
                                  valentine
                                  ==================== U+2665 ISOpub -->

<!ENTITY diams    "&#9830;" ><!-- black diamond suit
                                  ==================== U+2666 ISOpub -->
